name: "CodeQL Config"

disable-default-queries: false

queries:
  - uses: security-and-quality

paths-ignore:
  - "node_modules"
  - "dist"
  - ".next"
  - "coverage"
  - "test-results"
  - "playwright-report"
  - "**/*.test.ts"
  - "**/*.test.js"
  - "**/*.spec.ts"
  - "**/*.spec.js"
  - "**/__tests__/**"
  - "**/tests/**"
  - "**/*.config.js"
  - "**/*.config.ts"
  - "tailwind.config.js"
  - "next.config.js"
  - "vitest.config.ts"
  - "playwright.config.ts"

paths:
  - "src"
  - "database"
  - "lib"
