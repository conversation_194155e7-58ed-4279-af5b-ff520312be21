{"numTotalTestSuites": 21, "numPassedTestSuites": 21, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 70, "numPassedTests": 70, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1756469530220, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Admin Stats API"], "fullName": "Admin Stats API should return admin statistics for authenticated admin", "status": "passed", "title": "should return admin statistics for authenticated admin", "duration": 2479.8612319999957, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Stats API"], "fullName": "Admin Stats API should require admin authentication", "status": "passed", "title": "should require admin authentication", "duration": 94.77374299999792, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Stats API"], "fullName": "Admin Stats API should reject non-admin users", "status": "passed", "title": "should reject non-admin users", "duration": 57.26212200000009, "failureMessages": [], "meta": {}}], "startTime": 1756469602266, "endTime": 1756469604898.2622, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/admin-stats.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify component modules can be imported", "status": "passed", "title": "should verify component modules can be imported", "duration": 488.19594000000507, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify admin component modules can be imported", "status": "passed", "title": "should verify admin component modules can be imported", "duration": 685.8265790000005, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify company dashboard components can be imported", "status": "passed", "title": "should verify company dashboard components can be imported", "duration": 217.72800299999653, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify batch benefit selection component can be imported", "status": "passed", "title": "should verify batch benefit selection component can be imported", "duration": 45.89361499999359, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify analytics components can be imported", "status": "passed", "title": "should verify analytics components can be imported", "duration": 128.28409099999408, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify benefit ranking component can be imported", "status": "passed", "title": "should verify benefit ranking component can be imported", "duration": 107.83105500000238, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Props Validation"], "fullName": "Component Integration Tests Component Props Validation should validate component props interface", "status": "passed", "title": "should validate component props interface", "duration": 35.56596900000295, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Props Validation"], "fullName": "Component Integration Tests Component Props Validation should validate benefit ranking props interface", "status": "passed", "title": "should validate benefit ranking props interface", "duration": 32.821613000007346, "failureMessages": [], "meta": {}}], "startTime": 1756469604979, "endTime": 1756469606721.8215, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/component-integration.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should handle user profile retrieval", "status": "passed", "title": "should handle user profile retrieval", "duration": 968.4990250000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should handle user email change", "status": "passed", "title": "should handle user email change", "duration": 2555.2993899999997, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should reject unauthorized profile access", "status": "passed", "title": "should reject unauthorized profile access", "duration": 117.29113199999938, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should handle invalid session tokens", "status": "passed", "title": "should handle invalid session tokens", "duration": 408.43730699999924, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should display all companies on the start page without filters", "status": "passed", "title": "should display all companies on the start page without filters", "duration": 2390.2798820000007, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should return companies with pagination and filters", "status": "passed", "title": "should return companies with pagination and filters", "duration": 161.05766399999993, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by location", "status": "passed", "title": "should filter companies by location", "duration": 3252.589175, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by benefits", "status": "passed", "title": "should filter companies by benefits", "duration": 281.8614119999984, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by industry", "status": "passed", "title": "should filter companies by industry", "duration": 127.4636929999997, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by size", "status": "passed", "title": "should filter companies by size", "duration": 136.5187989999995, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should return detailed company information", "status": "passed", "title": "should return detailed company information", "duration": 108.19323500000064, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should handle non-existent company", "status": "passed", "title": "should handle non-existent company", "duration": 119.72463400000015, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should search companies by name", "status": "passed", "title": "should search companies by name", "duration": 147.25339600000007, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should search companies by benefit name", "status": "passed", "title": "should search companies by benefit name", "duration": 153.06012100000044, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefits Management"], "fullName": "Comprehensive App Functionality Integration Tests Benefits Management should return benefits in correct format", "status": "passed", "title": "should return benefits in correct format", "duration": 1035.9695599999995, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefits Management"], "fullName": "Comprehensive App Functionality Integration Tests Benefits Management should filter benefits by category", "status": "passed", "title": "should filter benefits by category", "duration": 114.08700099999987, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefits Management"], "fullName": "Comprehensive App Functionality Integration Tests Benefits Management should search benefits by name", "status": "passed", "title": "should search benefits by name", "duration": 96.5422520000011, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Management"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Management should allow authenticated users to add benefits to their company", "status": "passed", "title": "should allow authenticated users to add benefits to their company", "duration": 1858.7001670000009, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Management"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Management should reject unauthorized benefit additions", "status": "passed", "title": "should reject unauthorized benefit additions", "duration": 119.89670500000102, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Management"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Management should allow users to remove benefits from their company", "status": "passed", "title": "should allow users to remove benefits from their company", "duration": 2264.2232879999992, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to create benefit rankings", "status": "passed", "title": "should allow users to create benefit rankings", "duration": 1182.7794949999989, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to update benefit rankings", "status": "passed", "title": "should allow users to update benefit rankings", "duration": 136.82002099999954, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to add benefits to existing rankings", "status": "passed", "title": "should allow users to add benefits to existing rankings", "duration": 1628.6623690000015, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to remove benefits from rankings", "status": "passed", "title": "should allow users to remove benefits from rankings", "duration": 126.14849099999992, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to reset all benefit rankings", "status": "passed", "title": "should allow users to reset all benefit rankings", "duration": 122.74942400000145, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should retrieve user benefit rankings", "status": "passed", "title": "should retrieve user benefit rankings", "duration": 94.76632899999822, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Missing Company Reports"], "fullName": "Comprehensive App Functionality Integration Tests Missing Company Reports should allow users to report missing companies", "status": "passed", "title": "should allow users to report missing companies", "duration": 671.0452359999981, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Missing Company Reports"], "fullName": "Comprehensive App Functionality Integration Tests Missing Company Reports should prevent duplicate missing company reports", "status": "passed", "title": "should prevent duplicate missing company reports", "duration": 173.33114999999816, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Missing Company Reports"], "fullName": "Comprehensive App Functionality Integration Tests Missing Company Reports should allow users to view their missing company reports", "status": "passed", "title": "should allow users to view their missing company reports", "duration": 593.547767, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should allow users to save companies", "status": "passed", "title": "should allow users to save companies", "duration": 686.3304199999984, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should allow users to unsave companies", "status": "passed", "title": "should allow users to unsave companies", "duration": 10882.416444999999, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should retrieve user saved companies", "status": "passed", "title": "should retrieve user saved companies", "duration": 161.90943899999547, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should prevent duplicate saved companies", "status": "passed", "title": "should prevent duplicate saved companies", "duration": 118.6468160000004, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should allow premium users to access analytics", "status": "passed", "title": "should allow premium users to access analytics", "duration": 1983.6918360000054, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should restrict non-premium users to preview analytics", "status": "passed", "title": "should restrict non-premium users to preview analytics", "duration": 130.34964699999546, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should track analytics events", "status": "passed", "title": "should track analytics events", "duration": 3414.9665969999987, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should handle anonymous analytics tracking", "status": "passed", "title": "should handle anonymous analytics tracking", "duration": 3460.4047159999973, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should provide benefit ranking analytics for premium users", "status": "passed", "title": "should provide benefit ranking analytics for premium users", "duration": 1593.6787790000017, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefit Verification"], "fullName": "Comprehensive App Functionality Integration Tests Benefit Verification should allow users to submit benefit verifications", "status": "passed", "title": "should allow users to submit benefit verifications", "duration": 1204.6733920000042, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefit Verification"], "fullName": "Comprehensive App Functionality Integration Tests Benefit Verification should prevent duplicate benefit verifications", "status": "passed", "title": "should prevent duplicate benefit verifications", "duration": 274.6140860000014, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefit Verification"], "fullName": "Comprehensive App Functionality Integration Tests Benefit Verification should allow users to view their benefit verification submissions", "status": "passed", "title": "should allow users to view their benefit verification submissions", "duration": 2659.211731999996, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to view pending benefit verifications", "status": "passed", "title": "should allow admins to view pending benefit verifications", "duration": 1741.1656460000013, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to approve benefit verifications", "status": "passed", "title": "should allow admins to approve benefit verifications", "duration": 3215.2554759999985, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to reject benefit verifications", "status": "passed", "title": "should allow admins to reject benefit verifications", "duration": 603.1878530000031, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to view missing company reports", "status": "passed", "title": "should allow admins to view missing company reports", "duration": 3728.554305999998, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to approve missing company reports", "status": "passed", "title": "should allow admins to approve missing company reports", "duration": 3068.192229, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to reset user analytics", "status": "passed", "title": "should allow admins to reset user analytics", "duration": 1204.6528780000008, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should restrict admin endpoints to admin users only", "status": "passed", "title": "should restrict admin endpoints to admin users only", "duration": 149.12436300000263, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should log activity when admin deletes a user", "status": "passed", "title": "should log activity when admin deletes a user", "duration": 2297.5593450000015, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle malformed JSON requests", "status": "passed", "title": "should handle malformed JSON requests", "duration": 245.16467099999863, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle missing required fields", "status": "passed", "title": "should handle missing required fields", "duration": 127.9935570000016, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle database connection errors gracefully", "status": "passed", "title": "should handle database connection errors gracefully", "duration": 133.6796719999984, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 477.29415000000154, "failureMessages": [], "meta": {}}], "startTime": 1756469537431, "endTime": 1756469602156.2942, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/comprehensive-app-functionality.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should connect to the database", "status": "passed", "title": "should connect to the database", "duration": 1.6015469999983907, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should verify required tables exist", "status": "passed", "title": "should verify required tables exist", "duration": 5.127202999996371, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to query benefit categories", "status": "passed", "title": "should be able to query benefit categories", "duration": 1.7767949999979464, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to query benefits", "status": "passed", "title": "should be able to query benefits", "duration": 1.6535730000032345, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to query companies", "status": "passed", "title": "should be able to query companies", "duration": 1.5092799999983981, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to insert and delete test data", "status": "passed", "title": "should be able to insert and delete test data", "duration": 4.023110000009183, "failureMessages": [], "meta": {}}], "startTime": 1756469606757, "endTime": 1756469606773.0232, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/simple-database.test.ts"}]}